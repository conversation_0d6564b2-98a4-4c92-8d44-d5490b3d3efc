name: Docker Build and Publish Docker Image

on:
  push:
    branches:
      - main
    paths:
      - main.py
      - utils.py
      - core/**
      - core
      - Dockerfile
      - .dockerignore
      - requirements.txt
      - docker-compose.yml
      - .github/workflows/main.yml
      - .gitmodules
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
      with:
        submodules: recursive
        token: ${{ secrets.PACK_TOKEN }}

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v1

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1.6.0

    - name: Login to Docker Hub
      uses: docker/login-action@v3.0.0
      with:
        username: ${{ secrets.DOCKER_HUB_USERNAME }}
        password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.PACK_TOKEN }}

    - name: Get current version
      id: get_version
      run: |
        VERSION=$(cat VERSION || echo "0.0.0")
        echo "Current version: $VERSION"
        echo "version=$VERSION" >> $GITHUB_OUTPUT

    - name: Bump version
      id: bump_version
      run: |
        IFS='.' read -ra VERSION_PARTS <<< "${{ steps.get_version.outputs.version }}"
        PATCH=$((VERSION_PARTS[2] + 1))
        NEW_VERSION="${VERSION_PARTS[0]}.${VERSION_PARTS[1]}.$PATCH"
        echo $NEW_VERSION > VERSION
        echo "New version: $NEW_VERSION"
        echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT

    - name: Commit version bump
      env:
        GITHUB_TOKEN: ${{ secrets.PAT_TOKEN }}
      run: |
        git config --global user.name 'github-actions[bot]'
        git config --global user.email 'github-actions[bot]@users.noreply.github.com'
        git add VERSION
        git commit -m "📖 Bump version to ${{ steps.bump_version.outputs.new_version }}"
        git push

    - name: Build and push Docker image
      uses: docker/build-push-action@v2.7.0
      with:
        context: .
        file: Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: |
          yym68686/uni-api:latest
          yym68686/uni-api:${{ steps.bump_version.outputs.new_version }}
          ghcr.io/${{ github.repository }}:latest
          ghcr.io/${{ github.repository }}:${{ steps.bump_version.outputs.new_version }}