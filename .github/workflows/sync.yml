name: Sync Fork

on:
  schedule:
    - cron: '0 0 * * *'  # 每天凌晨执行
    # - cron: '0 */12 * * *'  # 每12小时执行一次
  workflow_dispatch:      # 支持手动触发

jobs:
  sync:
    runs-on: ubuntu-latest
    if: github.repository != 'yym68686/uni-api'

    steps:
      - name: Checkout target repo
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 0  # 获取所有历史记录，以确保正确同步
          token: ${{ secrets.PAT }}  # 使用PAT替代GITHUB_TOKEN

      - name: Sync Fork
        uses: aormsby/Fork-Sync-With-Upstream-action@v3.4.1
        with:
          target_repo_token: ${{ secrets.PAT }}
          upstream_sync_repo: yym68686/uni-api
          upstream_sync_branch: main
          target_sync_branch: main
          upstream_pull_args: --allow-unrelated-histories --no-edit --strategy-option theirs
          test_mode: false

      - name: Check for new commits
        if: steps.sync.outputs.has_new_commits == 'true'
        run: echo "新的提交已同步。"

      - name: No new commits
        if: steps.sync.outputs.has_new_commits == 'false'
        run: echo "没有新的提交需要同步。"